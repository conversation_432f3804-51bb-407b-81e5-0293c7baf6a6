﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace PSSmartMemo.Model;

public partial class Delegation
{
    public int Id { get; set; }

    public string FromUserId { get; set; }

    public string ToUserId { get; set; }

    public string Comments { get; set; }

    public string Type { get; set; }

    public DateTime? DateFrom { get; set; }

    public DateTime? DateTo { get; set; }

    public bool IsActive { get; set; }

    public string CraatedBy { get; set; }

    public DateTime CreatedDate { get; set; }

    public string ModifiedBy { get; set; }

    public DateTime? ModifiedDate { get; set; }

    public virtual User FromUser { get; set; }

    public virtual User ToUser { get; set; }
}