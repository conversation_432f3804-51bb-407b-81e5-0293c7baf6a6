using Microsoft.EntityFrameworkCore;
using PSSmartMemo.Extensions;

namespace PSSmartMemo.Services;

public class WatchListDataService(IDbContextFactory<ApplicationDbContext> contextFactory)
{

    public async Task<List<MemoDto>> GetMyWatchList(string userId)
    {
        await using var dc = await contextFactory.CreateDbContextAsync();
        var normalizedUserId = userId.ToLower();
        var twoMonthsAgo = DateTime.Now.AddMonths(-2);

        // Single query to get all required data with proper joins and includes
        var query = from memo in dc.Memos
                    join status in dc.MemoStatuses on memo.MemoStatusId equals status.Id
                    join template in dc.MemoTemplates on memo.MemoTemplateId equals template.MemoTemplateId
                    join memoType in dc.MemoTypes on template.MemoTypeId equals memoType.MemoTypeId
                    join creator in dc.Users on memo.MemoCreatedBy.ToLower() equals creator.UserId.ToLower()
                    where dc.MemoApprovalLogs.Any(log =>
                        (log.FromApprover.MemoApproverUserId.ToLower() == normalizedUserId ||
                         log.ToApprover.MemoApproverUserId.ToLower() == normalizedUserId ||
                         log.DelegatedUserId == normalizedUserId) &&
                        log.MemoId == memo.MemoId)
                    let lastLog = dc.MemoApprovalLogs
                        .Where(log => log.MemoId == memo.MemoId)
                        .OrderByDescending(log => log.ActionDate)
                        .Select(log => new
                        {
                            log.ActionDate,
                            ActionTitle = log.ApprovalActions.ApprovalActionTitle,
                            FromApproverName = log.FromApprover.MemoApproverUserName,
                            ToApproverName = log.ToApprover.MemoApproverUserName,
                            DelegatedUserName = log.DelegatedUser.Name
                        })
                        .FirstOrDefault()
                    where lastLog == null ||
                          !(lastLog.ActionTitle == "approved" || lastLog.ActionTitle == "rejected") ||
                          lastLog.ActionDate > twoMonthsAgo
                    select new MemoDto
                    {
                        MemoTypeStr = memoType.MemoTypeName,
                        MemoTyeShort = memoType.MemoTypeCode,
                        MemoId = memo.MemoId,
                        MemoCode = memo.MemoCode,
                        MemoTitle = memo.MemoTitle,
                        MemoStatus = status.MemoStatusTitle,
                        MemoCreatedDate = memo.MemoCreatedDate,
                        MemoCreatedBy = creator.Name, // Directly get the creator name
                        LastAction = lastLog != null ? lastLog.ActionTitle : null,
                        LastActionDate = lastLog != null ? lastLog.ActionDate : (DateTime?)null,
                        LastActionBy = lastLog != null ? (lastLog.FromApproverName ?? "") : "",
                        LastActionByDelegation = lastLog != null ? (lastLog.DelegatedUserName ?? "") : "",
                        RequiredApprovalFrom = lastLog != null ? (lastLog.ToApproverName ?? "") : "",
                        PendingAt = lastLog != null ? (lastLog.ToApproverName ?? "") : "",
                        ForwardedBy = lastLog != null ? (lastLog.FromApproverName ?? "") : "",
                        PendingSince = lastLog != null ? lastLog.ActionDate.GetTimeSpanString() : ""
                    };

        var userMemos = await query
            .Distinct()
            .OrderByDescending(m => m.LastActionDate)
            .AsNoTracking()
            .ToListAsync();

        // Post-process delegation approval in memory (minimal loop)
        foreach (var memo in userMemos.Where(m =>
            m.LastActionBy != m.LastActionByDelegation &&
            !string.IsNullOrEmpty(m.LastActionByDelegation)))
        {
            memo.DelegatinApproval = memo.LastActionByDelegation;
        }

        return userMemos;
    }
    public async Task<List<MemoDto>> GetMyWatchListOld(string userId)
    {
        await using var dc = await contextFactory.CreateDbContextAsync();

        userId = userId.ToLower();
        var twoMonthsAgo = DateTime.Now.AddMonths(-2);

        var userMemos = await (from a in dc.MemoApprovalLogs
            join st in dc.MemoStatuses on a.Memo.MemoStatusId equals st.Id
            where (a.FromApprover.MemoApproverUserId.ToLower() == userId
                   || a.ToApprover.MemoApproverUserId.ToLower() == userId
                   || a.DelegatedUserId == userId)
            let lastAction = (from log in dc.MemoApprovalLogs
                where log.MemoId == a.MemoId
                orderby log.ActionDate descending
                select new { log.ApprovalActions.ApprovalActionTitle, log.ActionDate })
                .FirstOrDefault()
            where lastAction == null ||
                  !(lastAction.ApprovalActionTitle.ToLower() == "approved" || lastAction.ApprovalActionTitle.ToLower()== "rejected") ||
                  lastAction.ActionDate > twoMonthsAgo
orderby a.ActionDate descending
                               select new MemoDto
                               {
                                   MemoTypeStr = a.Memo.MemoTemplate.MemoType.MemoTypeName,
                                   MemoTyeShort = a.Memo.MemoTemplate.MemoType.MemoTypeCode,
                                   MemoId = a.MemoId,
                                   MemoCode = a.Memo.MemoCode,
                                   MemoTitle = a.Memo.MemoTitle,
                                   MemoStatus = st.MemoStatusTitle,
                                   MemoCreatedDate = a.Memo.MemoCreatedDate,
                                   MemoCreatedBy = a.Memo.MemoCreatedBy,
                                   LastAction = lastAction.ApprovalActionTitle,
                                   LastActionDate = (from log in dc.MemoApprovalLogs
                                                     where log.MemoId == a.MemoId
                                                     orderby log.ActionDate descending
                                                     select log.ActionDate).FirstOrDefault(),
                                   LastActionBy = (from log in dc.MemoApprovalLogs
                                                   where log.MemoId == a.MemoId
                                                   orderby log.ActionDate descending
                                                   select log.FromApprover.MemoApproverUserName).FirstOrDefault() ?? "",
                                                   LastActionByDelegation = a.DelegatedUser.Name ?? "",
                                   RequiredApprovalFrom = (from log in dc.MemoApprovalLogs
                                                           where log.MemoId == a.MemoId
                                                           orderby log.ActionDate descending
                                                           select log.ToApprover.MemoApproverUserName).FirstOrDefault() ?? "",
                                   PendingAt = (from log in dc.MemoApprovalLogs
                                                where log.MemoId == a.MemoId
                                                orderby log.ActionDate descending
                                                select log.ToApprover.MemoApproverUserName).FirstOrDefault() ?? "",
                                   ForwardedBy = (from log in dc.MemoApprovalLogs
                                                  where log.MemoId == a.MemoId
                                                  orderby log.ActionDate descending
                                                  select log.FromApprover.MemoApproverUserName).FirstOrDefault() ?? "",
                                   PendingSince = (from log in dc.MemoApprovalLogs
                                                   where log.MemoId == a.MemoId
                                                   orderby log.ActionDate descending
                                                   select log.ActionDate.GetTimeSpanString()).FirstOrDefault()
                               })
            .Distinct()
            .ToListAsync();

        foreach (var memo in userMemos)
        {
            var user = await dc.Users
                .AsNoTracking()
                .FirstOrDefaultAsync(a => a.UserId.ToLower() == memo.MemoCreatedBy.ToLower());

            if (memo.LastActionBy != memo.LastActionByDelegation && !string.IsNullOrEmpty(memo.LastActionByDelegation))
            {
                memo.DelegatinApproval = memo.LastActionByDelegation;
            }
            if (user != null)
                {
                    memo.MemoCreatedBy = user.Name;
                }
        }

        return userMemos;
    }

    public async Task<MemoDto?> GetWatchMemoDetail(int memoId, string userId)
    {
        await using var dc = await contextFactory.CreateDbContextAsync();

        userId = userId.ToLower();

        // Security check: Verify if user is involved in this memo's approval process
        var isUserInvolved = dc.MemoApprovalLogs.Any(a =>
            (a.FromApprover.MemoApproverUserId.ToLower() == userId ||
             a.ToApprover.MemoApproverUserId.ToLower() == userId ||
             a.DelegatedUserId == userId) &&
            a.MemoId == memoId);

        if (!isUserInvolved)
        {
            return null;
        }

        var memo = (from a in dc.Memos
            join st in dc.MemoStatuses on a.MemoStatusId equals st.Id
            where a.MemoId == memoId
            select new MemoDto
            {
                MemoId = a.MemoId,
                MemoCode = a.MemoCode,
                MemoTitle = a.MemoTitle,
                MemoStatus = st.MemoStatusTitle,
                MemoCreatedDate = a.MemoCreatedDate,
                MemoCreatedBy = a.MemoCreatedBy,
                MemoTypeId = a.MemoTypeId,
                MemoTypeStr = a.MemoTemplate.MemoType.MemoTypeName,
                InitiatedBy = dc.Users.FirstOrDefault(c=>c.UserId==a.MemoCreatedBy).Name??"",
                LastAction = (from log in dc.MemoApprovalLogs
                    where log.MemoId == a.MemoId
                    orderby log.ActionDate descending
                    select log.ApprovalActions.ApprovalActionTitle).FirstOrDefault() ?? "",
                LastActionDate = (from log in dc.MemoApprovalLogs
                    where log.MemoId == a.MemoId
                    orderby log.ActionDate descending
                    select log.ActionDate).FirstOrDefault(),
                LastActionBy = (from log in dc.MemoApprovalLogs
                    where log.MemoId == a.MemoId
                    orderby log.ActionDate descending
                    select log.FromApprover.MemoApproverUserName).FirstOrDefault() ?? "",
                RequiredApprovalFrom = (from log in dc.MemoApprovalLogs
                    where log.MemoId == a.MemoId
                    orderby log.ActionDate descending
                    select log.ToApprover.MemoApproverUserName).FirstOrDefault() ?? "",
                MemoSections = (from b in a.MemoSections
                    orderby b.MemoSectionSortOrder
                    where b.MemoSectionIsActive
                          && b.MemoSectionIsDel == false
                          && b.MemoSectionIgnored == false

                                select new MemoSection
                    {
                        MemoSectionId = b.MemoSectionId,
                        MemoSectionTitle = b.MemoSectionTitle,
                        MemoSectionContentHtml = b.MemoSectionContentHtml,
                        MemoSectionSortOrder = b.MemoSectionSortOrder,
                        MemoSectionIsActive = b.MemoSectionIsActive,
                        MemoSectionIsDel = b.MemoSectionIsDel,
                        MemoSectionIgnored = b.MemoSectionIgnored
                    }).ToList(),
                MemoApprovers = (from c in a.MemoApprovers
                    select new MemoApprover
                    {
                        MemoApproverId = c.MemoApproverId,
                        MemoApproverRoleId = c.MemoApproverRoleId,
                        MemoApproverUserName = c.MemoApproverRole.MemoApproverRoleTitle
                    }).ToList(),
                MemoAttachments = (from d in a.MemoAttachments
                    where d.MemoAttachmentIsDel == false &&
                          d.MemoAttachmentIsActive
                    select new MemoAttachment
                    {
                        MemoAttachmentId = d.MemoAttachmentId,
                        MemoAttachmentTitle = d.MemoAttachmentTitle
                    }).ToList()
            }).FirstOrDefault();

        if (memo == null)
        {
            return null;
        }

        var creator = dc.Users.FirstOrDefault(u => u.UserId.ToLower() == memo.MemoCreatedBy.ToLower());
        if (creator != null)
        {
            memo.MemoCreatedBy = creator.Name;
        }

        return memo;
    }

    public async Task<List<MemoAttachmentDto>> GetMemoAttachments(int memoId)
    {
        await using var dc = await contextFactory.CreateDbContextAsync();

        var q = (from a in dc.MemoAttachments
            where a.MemoId == memoId && a.MemoAttachmentIsDel == false && a.MemoAttachmentIsActive == true
            select new MemoAttachmentDto
            {
                AttachmentTypeId = a.MemoAttachmentId,
                Name = a.MemoAttachmentDocName,
                Path = AppDataService.ConvertToUrl(a.MemoAttachmentFilePath),
                Type = a.MemoAttachmentDocType,
                Size = (a.MemoAttachmentDocSizeMb.HasValue ? a.MemoAttachmentDocSizeMb.Value.ToString() : "0")
            }).ToList();

        return await Task.FromResult(q);
    }

    public Task<List<MemoApproverDto>> GetMemoApprovers(int memoId)
    {
        using var dc = contextFactory.CreateDbContext();
        var q = (from a in dc.MemoApprovers
                 where a.MemoId == memoId &&
                 a.MemoApproverRole.MemoApproverRoleTitle!="Initiator"
                 orderby a.MemoApproverSortOrder
                 select new MemoApproverDto
                 {
                     Id = a.Id,
                     MemoApproverId = a.MemoApproverId,
                     UserId = a.MemoApproverUserId,
                     User = a.MemoApproverUserName,
                     Role = a.MemoApproverRole.MemoApproverRoleTitle,
                     Email = a.MemoApproverUserEmail,
                     Title = a.MemoApproverTitle,
                     SortOrder = a.MemoApproverSortOrder,
                     AllowType = a.MemoAllowType,
                     UserRoleType = a.MemoApproverRole.MemoApproverRoleType,
                     //MemoApproverDesignation = a.MemoApproverUserDetail,
                     Designation = a.Designation,
                     Department = a.Department,
                     //Department = a.MemoApproverRole.MemoApproverRoleUserDept,
                     LastAction = (from b in dc.MemoApprovalLogs
                                   where b.MemoId == memoId && b.FromApproverId == a.MemoApproverId
                                   orderby b.ActionDate descending
                                   select b.ApprovalActions.ApprovalActionTitle).FirstOrDefault(),
                    
                 }).ToList();

        foreach (var item in q)
        {
            var log = (from b in dc.MemoApprovalLogs
                where b.MemoId == memoId && b.FromApproverId == item.MemoApproverId
                orderby b.ActionDate descending
                select new { b.ActionDate, b.DelegatedUserId, Name=b.DelegatedUser.Name }).FirstOrDefault();
            item.LastActionDate = log?.ActionDate;
            item.DelegatedUser = log?.Name;
            item.DelegatedUserId = log?.DelegatedUserId;
            // pending at, I want to mark status of memo approver Pending based on following condition, in MemoApproverLog ReplyLogId is null and compare user with ToApproverId

            // if both touserid and DelegatedUserId as same then set delegated user to empty string
            if (item.UserId == item.DelegatedUserId)
            {
                item.DelegatedUser = "";
            }
            var pendingAt = (from a in dc.MemoApprovalLogs
                             where a.ReplyLogId == null &&
                             a.MemoId == memoId &&
                             a.ToApprover.MemoApproverUserId == item.UserId &&
                             a.ToApprover.MemoApproverUserName == item.User
                             select a).Any();
            if (pendingAt) { item.LastAction = "Pending"; }
            // Set action color based on the action type
            item.ActionColor = item.LastAction?.ToLower() switch
            {
                "approved" => "#28a745", // Green
                "rejected" => "#dc3545", // Red
                "query" => "#ffc107",    // Yellow
                "object" => "#fd7e14",   // Orange
                "reply" => "#17a2b8",    // Cyan
                "pending" => "#1e90ff",  // blue
                _ => "#6c757d"           // Gray for no action or unknown actions
            };
        }

        foreach (var i in q)
        {
            if (i.LastAction == "Reply") i.LastAction = "Approved";
        }

        return Task.FromResult(q);
    }
    public async Task<List<MemoApproverDto>> GetMemoApprovers2(int memoId)
    {
        await using var dc = await contextFactory.CreateDbContextAsync();

        // Step 1: Query and materialize the data you need
        var approversData = await (from a in dc.MemoApprovers
            where a.MemoId == memoId && 
                  a.MemoApproverRole.MemoApproverRoleTitle != "Initiator"
            orderby a.MemoApproverSortOrder
            select a
        ).Include(a => a.MemoApproverRole)
         .Include(a => a.MemoApprovalLogFromApprovers)
         .Include(a => a.MemoApprovalLogToApprovers)
         .ToListAsync();

        // Step 2: Map to DTOs in memory, calling GetApproverStatus
        var approvers = approversData.Select(a => {
            // Get the latest log where this approver was either the from or to approver
            var latestLog = dc.MemoApprovalLogs
                .Where(log => log.MemoId == memoId && 
                             (log.FromApproverId == a.MemoApproverId || log.ToApproverId == a.MemoApproverId))
                
                .OrderByDescending(log => log.ActionDate)
                .FirstOrDefault();

            return new MemoApproverDto
            {
                Id = a.Id,
                MemoApproverId = a.MemoApproverId,
                UserId = a.MemoApproverUserId,
                User = a.MemoApproverUserName,
                Role = a.MemoApproverRole?.MemoApproverRoleTitle,
                Email = a.MemoApproverUserEmail,
                Title = a.MemoApproverTitle,
                Status = GetApproverStatus(a, memoId),
                SortOrder = a.MemoApproverSortOrder,
                AllowType = a.MemoAllowType,
                UserRoleType = a.MemoApproverRole?.MemoApproverRoleType,
                //MemoApproverDesignation = a.MemoApproverUserDetail,
                Designation = a.Designation,
                Department = a.Department,
                //Department = a.MemoApproverRole?.MemoApproverRoleUserDept,
                LastAction = latestLog?.ApprovalActions?.ApprovalActionTitle,
                LastActionDate = latestLog?.ActionDate
            };
        }).ToList();

        return approvers;
    }

    private string GetApproverStatus(MemoApprover approver, int memoId)
    {
        // Check if this approver has any approval logs
        var latestLog = (from log in approver.MemoApprovalLogToApprovers
            where log.MemoId == memoId
            orderby log.ActionDate descending
            select log).FirstOrDefault();

        if (latestLog == null)
        {
            return "Pending";
        }

        // Check if this log has been forwarded (meaning the approver has taken action)
        if (latestLog.IsForwarded)
        {
            return latestLog.ApprovalActions?.ApprovalActionTitle ?? "Processed";
        }

        return "Pending";
    }
}
