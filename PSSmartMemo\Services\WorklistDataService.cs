using PSSmartMemo.Extensions;
using Syncfusion.Blazor.FileManager.Internal;

namespace PSSmartMemo.Services;

public class WorklistDataService(IDbContextFactory<ApplicationDbContext> contextFactory, CorporateService corporateService)
{
    public Task<List<MemoDto>> GetMyWorkList(string userId)
    {
        var dc = contextFactory.CreateDbContext();
        userId = userId.ToLower();
        var userMemos = (from a in dc.MemoApprovalLogs
            where a.ToApprover.MemoApproverUserId.ToLower() == userId
                  && a.ReplyLogId == null
            select new MemoDto
            {
                MemoApprovalLogId = a.MemoApprovalLogId,
                MemoTypeStr = a.Memo.MemoTemplate.MemoType.MemoTypeName,
                MemoTyeShort = a.Memo.MemoTemplate.MemoType.MemoTypeCode,
                MemoId = a.MemoId,
                MemoCode = a.Memo.MemoCode,
                MemoTitle = a.Memo.MemoTitle,
                MemoStatus = a.Memo.MemoStatus,
                MemoCreatedDate = a.Memo.MemoCreatedDate,
                MemoCreatedBy = a.Memo.MemoCreatedBy,
                PendingAt = a.ToApprover.MemoApproverUserName,
                PendingSince = a.ActionDate.GetTimeSpanString(),
                ForwardedBy = a.FromApprover.MemoApproverUserName,
                LastAction = a.ApprovalActions.ApprovalActionTitle,
                ActionDate = a.ActionDate,
                MemoCreatedByUserId = a.Memo.MemoCreatedBy

            }).Distinct().OrderByDescending(m => m.ActionDate).ToList();

        foreach (var i in userMemos)
        {
            var uu = (from a in dc.Users
                      where a.UserId.ToLower() == i.MemoCreatedBy.ToLower()
                      select a).FirstOrDefault();
            if (uu != null)
            {
                i.MemoCreatedBy = uu.Name;
            }
        }
        return Task.FromResult(userMemos);
    }

    public Task<List<MemoDto>> GetDelegatedWorkList(List<string> delegatedFromUserIds)
    {
        var dc = contextFactory.CreateDbContext();

        if (!delegatedFromUserIds.Any())
            return Task.FromResult(new List<MemoDto>());

        var delegatedMemos = (from a in dc.MemoApprovalLogs
            where delegatedFromUserIds.Contains(a.ToApprover.MemoApproverUserId.ToLower())
                  && a.ReplyLogId == null
            select new MemoDto
            {
                MemoApprovalLogId = a.MemoApprovalLogId,
                MemoTypeStr = a.Memo.MemoTemplate.MemoType.MemoTypeName,
                MemoTyeShort = a.Memo.MemoTemplate.MemoType.MemoTypeCode,
                MemoId = a.MemoId,
                MemoCode = a.Memo.MemoCode,
                MemoTitle = a.Memo.MemoTitle,
                MemoStatus = a.Memo.MemoStatus,
                MemoCreatedDate = a.Memo.MemoCreatedDate,
                MemoCreatedBy = a.Memo.MemoCreatedBy,
                PendingAt = a.ToApprover.MemoApproverUserName,
                PendingSince = a.ActionDate.GetTimeSpanString(),
                ForwardedBy = a.FromApprover.MemoApproverUserName,
                LastAction = a.ApprovalActions.ApprovalActionTitle,
                ActionDate = a.ActionDate
            }).Distinct().OrderByDescending(m => m.ActionDate).ToList();

        foreach (var i in delegatedMemos)
        {
            var uu = (from a in dc.Users
                      where a.UserId.ToLower() == i.MemoCreatedBy.ToLower()
                      select a).FirstOrDefault();
            if (uu != null)
            {
                i.MemoCreatedBy = uu.Name;
            }
        }
        return Task.FromResult(delegatedMemos);
    }

    public Task<bool> IsMyMemoItem(int logId, string userId)
    {
        var dc = contextFactory.CreateDbContext();

        // Check if memo belongs directly to the user
        var isDirectMemo = dc.MemoApprovalLogs.Any(c => c.MemoApprovalLogId == logId
                                              && c.ToApprover.MemoApproverUserId == userId
                                              && c.ReplyLogId == null);

        if (isDirectMemo)
            return Task.FromResult(true);

        // Check if memo belongs to a user who has delegated to current user
        var isDelegatedMemo = (from log in dc.MemoApprovalLogs
                              join delegation in dc.Delegations on log.ToApprover.MemoApproverUserId equals delegation.FromUserId
                              where log.MemoApprovalLogId == logId
                                    && delegation.ToUserId.ToLower() == userId.ToLower()
                                    && delegation.IsActive == true
                                    && delegation.DateFrom <= DateTime.Now
                                    && delegation.DateTo >= DateTime.Now
                                    && log.ReplyLogId == null
                              select log).Any();

        return Task.FromResult(isDelegatedMemo);
    }

    public Task<MemoDto> GetMemoByLogId(int id)
    {
        var dc = contextFactory.CreateDbContext();
        var memo = (from a in dc.MemoApprovalLogs
            where a.MemoApprovalLogId == id
            select new MemoDto
            {
                MemoId = a.MemoId,
                MemoCode = a.Memo.MemoCode,
                MemoTitle = a.Memo.MemoTitle,
                MemoStatus = a.Memo.MemoStatus,
                MemoCreatedDate = a.Memo.MemoCreatedDate,
                MemoCreatedBy = a.Memo.MemoCreatedBy,
                MemoTypeId = a.Memo.MemoTypeId,
                MemoTypeStr = a.Memo.MemoTemplate.MemoType.MemoTypeName,
                InitiatedBy = dc.Users.FirstOrDefault(c=>c.UserId==a.Memo.MemoCreatedBy).Name??"",
                MemoSections = (from b in a.Memo.MemoSections
                    orderby b.MemoSectionSortOrder
                    where b.MemoSectionIsActive &&
                    b.MemoSectionIgnored==false
                          && b.MemoSectionIsDel == false
                    select new MemoSection
                    {
                        MemoSectionId = b.MemoSectionId,
                        MemoSectionTitle = b.MemoSectionTitle,
                        MemoSectionContentHtml = b.MemoSectionContentHtml,
                        MemoSectionSortOrder = b.MemoSectionSortOrder,
                        MemoSectionIsActive = b.MemoSectionIsActive,
                        MemoSectionIsDel = b.MemoSectionIsDel
                    }).ToList(),
                MemoApprovers = (from c in a.Memo.MemoApprovers
                    select new MemoApprover
                    {
                        MemoApproverId = c.MemoApproverId,
                        MemoApproverRoleId = c.MemoApproverRoleId,
                        MemoApproverUserName = c.MemoApproverRole.MemoApproverRoleTitle
                    }).ToList(),
                MemoAttachments = (from d in a.Memo.MemoAttachments
                    where d.MemoAttachmentIsDel == false &&
                          d.MemoAttachmentIsActive
                    select new MemoAttachment
                    {
                        MemoAttachmentId = d.MemoAttachmentId,
                        MemoAttachmentTitle = d.MemoAttachmentTitle
                    }).ToList()
            }).First();
        return Task.FromResult(memo);
    }

    public Task<List<MemoApprovalLogDto>> GetMemoApprovalLogs(int memoId)
    {
        var dc = contextFactory.CreateDbContext();
        var q = (from a in dc.MemoApprovalLogs
                 where a.MemoId == memoId
                 orderby a.ActionDate
                 select new MemoApprovalLogDto
                 {
                     MemoApprovalLogId = a.MemoApprovalLogId,
                     Action = a.ApprovalActions.ApprovalActionTitle,
                     ActionDateTime = a.ActionDate,
                     Comments = a.Comments,
                     FromUser = a.FromApprover.MemoApproverTitle == "Initiator"
                         ? a.FromApprover.MemoApproverUserName
                         : (a.DelegatedUser != null && !string.IsNullOrEmpty(a.DelegatedUserId)
                             ? $"{a.FromApprover.MemoApproverUserName} (Delegated to {a.DelegatedUser.Name})"
                             : a.FromApprover.MemoApproverUserName + (a.ApprovalActions.ApprovalActionTitle == "Submit" ? " - Initiator" : "")),
                     ToUser = a.ToApprover.MemoApproverUserName,
                     Role =  string.IsNullOrEmpty( a.FromApprover.Designation) ?  a.FromApprover.MemoApproverTitle : a.FromApprover.Designation
                 }).ToList();
        return Task.FromResult(q);
    }

    public Task<MemoApprovalLog> GetCurrentMemoState(int logId)
    {
        var dc = contextFactory.CreateDbContext();
        var q = (from a in dc.MemoApprovalLogs
            where a.MemoApprovalLogId == logId
            select new MemoApprovalLog
            {
                Comments = a.Comments, MemoId = a.MemoId, ActionDate = a.ActionDate,
                IsForwarded = a.IsForwarded, MemoApprovalLogId = a.MemoApprovalLogId, ToApproverId = a.ToApproverId,
                FromApproverId = a.FromApproverId, ApprovalActionsId = a.ApprovalActionsId,
                DraftComments=a.DraftComments, DraftActionId=a.DraftActionId, DraftToUserId=a.DraftToUserId
            }).First();
        return Task.FromResult(q);
    }

    private int? GetNextApproverId(int memoId, int currentApproverId)
    {
        var dc = contextFactory.CreateDbContext();
        var currentApproverSortOrder = dc.MemoApprovers
            .First(c => c.MemoApproverId == currentApproverId)
            .MemoApproverSortOrder ?? 0;

        var nextApprover = dc.MemoApprovers
            .Where(c => c.MemoId == memoId && c.MemoApproverSortOrder > currentApproverSortOrder)
            .OrderBy(c => c.MemoApproverSortOrder)
            .FirstOrDefault();
        // return current approver if there is no approver after him, so he can close the memo request
        if (nextApprover == null)
            return currentApproverId;

        return nextApprover?.MemoApproverId;
    }


    public Task<string> PerformReply(int logId, string comments, string userId)
    {
        var dc = contextFactory.CreateDbContext();
        var log = (from a in dc.MemoApprovalLogs
                   where a.MemoApprovalLogId == logId
                   select a).FirstOrDefault();
        int? nextApprover = 0;


        if (log != null)
        {
            if (log.ApprovalActionsId == 5) // Query
            {
                nextApprover = log.FromApproverId;
            }
            else if (log.ApprovalActionsId == 4)
            {
                nextApprover = GetNextApproverId(log.MemoId, log.ToApproverId ?? 0);
            }
            var sameUser = (from a in dc.MemoApprovalLogs
                            where a.MemoApprovalLogId == logId &&
                            a.ToApprover.MemoApproverUserId == userId
                            select a).Any();

            var newLog = new MemoApprovalLog
            {
                MemoId = log.MemoId,
                FromApproverId = log.ToApproverId ?? 0,
                ToApproverId = nextApprover, // Use calculated value
                ApprovalActionsId = 7,
                ActionDate = DateTime.Now,
                PrevReplyLogId = log.MemoApprovalLogId,
                IsForwarded = false,
                Comments = comments,
                DelegatedUserId = sameUser ? null : userId
                //DelegatedUserId = log.ToApprover.MemoApproverUserId==userId ? null : userId
            };



            var memo = dc.Memos.First(c => c.MemoId == log.MemoId);
            memo.MemoStatus = "PUBLISHED";
            memo.MemoStatusId = 2;
            memo.MemoModifiedDate=DateTime.Now;
            dc.SaveChanges();
            dc.MemoApprovalLogs.Add(newLog);
            dc.SaveChanges();

            log.IsForwarded = true;
            log.ReplyLogId = newLog.MemoApprovalLogId;
            //log.LastActionByUserId = userId;
        }

        dc.SaveChanges();

        return Task.FromResult("OK");
    }
    public Task<string> PerformClose(int logId, string comments, string action, string userId)
    {
        var dc = contextFactory.CreateDbContext();
        var tran = dc.Database.BeginTransaction();
        try
        {
            var log = (from a in dc.MemoApprovalLogs
                where a.MemoApprovalLogId == logId
                select a).FirstOrDefault();
            byte memoStatusId = 0;
            string memStatusText = "";
            byte actionId = 0;
            if (action == " (Approve)")
            {
                actionId = (byte)(ApprovalActionEnum.Approved);
                memoStatusId = 3;
                memStatusText = "APPROVED";
            }
            else if (action == " (Reject)")
            {
                actionId = (byte)ApprovalActionEnum.Rejected;
                memoStatusId = 4;
                memStatusText = "REJECTED";
            }

            if (actionId == 0)
                return Task.FromResult("Invalid action");

            var sameUser = (from a in dc.MemoApprovalLogs
                            where a.MemoApprovalLogId == logId &&
                            a.ToApprover.MemoApproverUserId == userId
                            select a).Any();

            if (log != null)
            {
                var newLog = new MemoApprovalLog
                {
                    MemoId = log.MemoId,
                    FromApproverId = log.ToApproverId ?? 0,
                    ToApproverId = null, // Use calculated value
                    ApprovalActionsId = actionId,
                    ActionDate = DateTime.Now,
                    PrevReplyLogId = log.MemoApprovalLogId,
                    IsForwarded = true,
                    Comments = comments,
                    //LastActionByUserId = userId
                    //DelegatedUserId = log.ToApprover.MemoApproverUserId == userId ? null : userId
                    DelegatedUserId = sameUser ? null : userId
                };

                dc.MemoApprovalLogs.Add(newLog);
                dc.SaveChanges();

                log.IsForwarded = true;
                log.ReplyLogId = newLog.MemoApprovalLogId;
                //log.LastActionByUserId = userId;

                var memo = dc.Memos.First(m => m.MemoId == log.MemoId);
                memo.MemoStatusId = memoStatusId;
                memo.MemoStatus = memStatusText;
                memo.MemoModifiedDate=DateTime.Now;
                dc.SaveChanges();
            }

            dc.SaveChanges();
            tran.Commit();
            return Task.FromResult("OK");
        }
        catch (Exception e)
        {
            tran.Rollback();
            Console.WriteLine(e);
            throw;
        }

    }


    public enum ApprovalActionEnum
    {
        Submit = 1,
        Approved = 2,
        Rejected = 3,
        Object = 4,
        Query = 5,
        Close = 6,
        Reply = 7,
        Skip = 8
    };

    public Task<string> PerformForward(int logId, MemoApprovalLog pLog, string userId)
    {
        var dc = contextFactory.CreateDbContext();
        //var nextApproverId = GetNextApproverId(pLog.MemoId, pLog.);
        var nextApproverId = GetNextApproverId(pLog.MemoId, pLog.ToApproverId ?? 0);
        if (nextApproverId != null)
        {
            var tran = dc.Database.BeginTransaction();
            var sameUser = (from a in dc.MemoApprovalLogs
                            where a.MemoApprovalLogId == pLog.MemoApprovalLogId &&
                            a.ToApprover.MemoApproverUserId == userId
                            select a).Any();
            string msg = "OK";
            try
            {
                var newLog = new MemoApprovalLog
                {
                    MemoId = pLog.MemoId,
                    FromApproverId = pLog.ToApproverId ?? 0,
                    ToApproverId = nextApproverId, // Use calculated value
                    ApprovalActionsId = (byte)ApprovalActionEnum.Approved,
                    ActionDate = DateTime.Now,
                    PrevReplyLogId = pLog.MemoApprovalLogId,
                    IsForwarded = false,
                    Comments = pLog.DraftComments,
                    DelegatedUserId = sameUser ? null : userId
                    
                };
                

                dc.MemoApprovalLogs.Add(newLog);
                dc.SaveChanges();
                var log = dc.MemoApprovalLogs.First(c => c.MemoApprovalLogId == logId);
                log.IsForwarded = true;
                log.ReplyLogId = newLog.MemoApprovalLogId;
                //log.LastActionByUserId = userId;

                dc.SaveChanges();
                tran.Commit();

            }
            catch (Exception ex)
            {
                tran.Rollback();
                msg = ex.Message;
                if (ex.InnerException != null)
                    msg += ex.InnerException.Message;

            }

            return Task.FromResult(msg);
        }

        return Task.FromResult("Next approver not found");
    }
    public async Task<string> PerformForward(int logId)
    {
        var dc = contextFactory.CreateDbContext();
        var log = dc.MemoApprovalLogs.FirstOrDefault(c => c.MemoApprovalLogId == logId);
        if (log == null)
        {
            return "Log entry not found";
        }

        int? nextApproverId = null;
        if (log.DraftActionId == 2)
        {
            nextApproverId = GetNextApproverId(log.MemoId, log.ToApproverId ?? 0);
        }
        else if (log.DraftActionId is 4 or 5)
        {
            nextApproverId = log.DraftToUserId;
        }
        else if(log.ApprovalActionsId is 3)
        {
            nextApproverId = log.FromApproverId;
        }

        if (true) // Combine conditions
        {
            var newLog = new MemoApprovalLog
            {
                MemoId = log.MemoId,
                FromApproverId = log.ToApproverId ?? 0,
                ToApproverId = nextApproverId, // Use calculated value
                ApprovalActionsId = log.DraftActionId,
                ActionDate = DateTime.Now,
                PrevReplyLogId = log.MemoApprovalLogId,
                IsForwarded = false,
                Comments = log.DraftComments
            };

            dc.MemoApprovalLogs.Add(newLog);
            dc.SaveChanges();

            log.IsForwarded = true;
            log.ReplyLogId = newLog.MemoApprovalLogId;
            dc.SaveChanges();

            // Send email notification to the next approver
            await SendEmailToNextApprover(logId, ((ApprovalActionEnum)log.DraftActionId).ToString());

            return "OK";
        }

        return "Invalid DraftActionId";
    }

    public Task<bool> CanForward(int logId)
    {
        var dc = contextFactory.CreateDbContext();
        var log = dc.MemoApprovalLogs.First(c => c.MemoApprovalLogId == logId);
        if ((log.IsForwarded == false || log.ApprovalActionsId==1) && log.DraftActionId != null)
        {
            return Task.FromResult(true);
        }
        return Task.FromResult(false);
    }

    public Task<bool> CanClose(int logId)
    {
        var dc = contextFactory.CreateDbContext();
        var log = dc.MemoApprovalLogs.First(c => c.MemoApprovalLogId == logId);
        return log.ToApproverId==log.FromApproverId && (log.ApprovalActionsId is 2 or 3) ? Task.FromResult(true) : Task.FromResult(false);
    }

    public Task<MemoApprovalLog> GetLogById(int logId)
    {
        var dc = contextFactory.CreateDbContext();
        var q = dc.MemoApprovalLogs.First(c => c.MemoApprovalLogId == logId);
        return Task.FromResult(q);
    }

    public Task<bool> IsObjectFormOpen(int logId)
    {
        var dc = contextFactory.CreateDbContext();
        var log = dc.MemoApprovalLogs.First(c=>c.MemoApprovalLogId==logId);
        return Task.FromResult( log.DraftActionId == 4);
    }

    public Task<bool> IsQueryFormOpen(int logId)
    {
        var dc = contextFactory.CreateDbContext();
        var log = dc.MemoApprovalLogs.First(c => c.MemoApprovalLogId == logId);
        return Task.FromResult(log.DraftActionId == 5);
    }
    public Task<bool> IsQueryMemo(int logId)
    {
        var dc = contextFactory.CreateDbContext();
        var log = dc.MemoApprovalLogs
            .Include(memoApprovalLog => memoApprovalLog.ApprovalActions)
            .First(c => c.MemoApprovalLogId == logId);
        return Task.FromResult(log.ApprovalActions.ApprovalActionTitle.ToLower() is "query" );
    }

    public Task<bool> IsObjectMemo(int logId)
    {
        var dc = contextFactory.CreateDbContext();
        var log = dc.MemoApprovalLogs
            .Include(memoApprovalLog => memoApprovalLog.ApprovalActions)
            .First(c => c.MemoApprovalLogId == logId);
        return Task.FromResult(log.ApprovalActions.ApprovalActionTitle.ToLower() is  "object");
    }

    public Task<bool> NextApproverExists(int logId)
    {
        var dc = contextFactory.CreateDbContext();
        var log = dc.MemoApprovalLogs.First(c => c.MemoApprovalLogId == logId);
        var toUserId = log.ToApproverId;
        // get toUserSortOrder

        var so = (from a in dc.MemoApprovers
            where a.MemoApproverId == log.ToApproverId
            select a.MemoApproverSortOrder ?? 0).First();

        // all approvers
        var q = (from a in dc.MemoApprovers
            orderby a.MemoApproverSortOrder
            where a.MemoId == log.MemoId &&
                  a.MemoApproverSortOrder>so
            select a).Any();

        return Task.FromResult(q);

    }
    public Task<bool> NextApproverExists2(int logId)
    {
        var dc = contextFactory.CreateDbContext();
        var log = dc.MemoApprovalLogs.First(c => c.MemoApprovalLogId == logId);
        var fromApprover = dc.MemoApprovers.FirstOrDefault(c=>c.MemoId==log.MemoId && c.MemoApproverId==log.FromApproverId);
        if (fromApprover == null) return Task.FromResult(false);
        // nextApprover
        var cc = (from a in dc.MemoApprovers
                  orderby a.MemoApproverSortOrder
                  where a.MemoId == log.MemoId &&
                  a.MemoApproverSortOrder > fromApprover.MemoApproverSortOrder
                  select a).FirstOrDefault();
        if (cc == null) return Task.FromResult(false);
        return Task.FromResult(true);
    }

    public Task<List<MemoApproverDto>> GetUpperApprovers(int logId)
    {
        var dc = contextFactory.CreateDbContext();
        var currentLog = dc.MemoApprovalLogs.First(c => c.MemoApprovalLogId == logId);

        var memoApprovers = (from a in dc.MemoApprovers
                             where a.MemoId==currentLog.MemoId && a.MemoApproverSkipped==false
                             select new MemoApproverDto
                             {
                                 Id = a.Id,
                                 MemoApproverId = a.MemoApproverId,
                                 UserId = a.MemoApproverUserId,
                                 User = a.MemoApproverUserName,
                                 Role = a.MemoApproverRole.MemoApproverRoleTitle,
                                 Email = a.MemoApproverUserEmail,
                                 Title = a.MemoApproverUserName + " (" + a.MemoApproverTitle + ")",
                                 Status = a.MemoApproverIsActive ? "Active" : "Inactive",
                                 SortOrder = a.MemoApproverSortOrder,
                                 AllowType = a.MemoAllowType,
                                 UserRoleType = a.MemoApproverRole.MemoApproverRoleType
                             }).ToList();
        var currentUserSort = memoApprovers.First(c => c.MemoApproverId == (currentLog.ToApproverId ?? 0));

        var previouseApprovers = memoApprovers.Where(c => c.SortOrder < currentUserSort.SortOrder).ToList();

        return Task.FromResult(previouseApprovers);
    }
    public Task<List<MemoApproverDto>> GetUpperApprovers2(int logId)
    {
        var dc = contextFactory.CreateDbContext();
        var currentLog = dc.MemoApprovalLogs.First(c => c.MemoApprovalLogId == logId);
        int currentApproverId = currentLog.FromApproverId;
        // get all those users who are belo to the current log ToApproverId
        var sortOrder = (from a in dc.MemoApprovers
                   where a.MemoId == currentLog.MemoId &&
                   a.MemoApproverId == currentApproverId
                   select a.MemoApproverSortOrder).First();

        var approvers = (from a in dc.MemoApprovers
                         where a.MemoId==currentLog.MemoId &&
                               a.MemoApproverId!=currentApproverId &&
                         a.MemoApproverSortOrder <= sortOrder
                         select new MemoApproverDto
                         {
                             Id = a.Id,
                             MemoApproverId = a.MemoApproverId,
                             UserId = a.MemoApproverUserId,
                             User = a.MemoApproverUserName,
                             Role = a.MemoApproverRole.MemoApproverRoleTitle,
                             Email = a.MemoApproverUserEmail,
                             Title = a.MemoApproverUserName + " (" +a.MemoApproverTitle + ")" ,
                             Status = a.MemoApproverIsActive ? "Active" : "Inactive",

                             SortOrder = a.MemoApproverSortOrder,
                             AllowType = a.MemoAllowType,
                             UserRoleType = a.MemoApproverRole.MemoApproverRoleType
                         }).ToList();
        return Task.FromResult(approvers);

    }


    public Task<string> PerformObject(int logId, int? toApproverId, string comments, string userId)
    {
        var dc = contextFactory.CreateDbContext();
        var tran = dc.Database.BeginTransaction();
        try
        {
            var log = (from a in dc.MemoApprovalLogs
                where a.MemoApprovalLogId == logId
                select a).FirstOrDefault();
            byte actionId = 4;

            if (toApproverId == null)
                return Task.FromResult("No destination approver is selected");
            var sameUser = (from a in dc.MemoApprovalLogs
                            where a.MemoApprovalLogId == logId &&
                            a.ToApprover.MemoApproverUserId == userId
                            select a).Any();
            if (log != null)
            {
                var newLog = new MemoApprovalLog
                {
                    MemoId = log.MemoId,
                    FromApproverId = log.ToApproverId ?? 0,
                    ToApproverId = toApproverId, // Use calculated value
                    ApprovalActionsId = actionId,
                    ActionDate = DateTime.Now,
                    PrevReplyLogId = log.MemoApprovalLogId,
                    IsForwarded = true,
                    Comments = comments,
                    //DelegatedUserId = log.ToApprover.MemoApproverUserId == userId ? null : userId
                    DelegatedUserId = sameUser ? null : userId

                };

                dc.MemoApprovalLogs.Add(newLog);
                dc.SaveChanges();

                log.IsForwarded = true;
                log.ReplyLogId = newLog.MemoApprovalLogId;
                //log.LastActionByUserId = userId;
                dc.SaveChanges();

                var toAppr = dc.MemoApprovers
                    .Include(memoApprover => memoApprover.MemoApproverRole).First(c => c.MemoApproverId == toApproverId);
                if (toAppr.MemoApproverRole.MemoApproverRoleTitle == "Initiator")
                {
                    var memo = dc.Memos.First(c => c.MemoId == log.MemoId);
                    memo.MemoStatusId = 1;
                    memo.MemoStatus = "DRAFT";
                    memo.MemoModifiedDate=DateTime.Now;
                    dc.SaveChanges();
                }
            }

            dc.SaveChanges();
            tran.Commit();
            return Task.FromResult("OK");
        }
        catch (Exception e)
        {
            tran.Rollback();
            Console.WriteLine(e);
            throw;
        }
    }

    public Task<string> PerformQuery(int logId, int? toApproverId, string comments, string userId)
    {
        var dc = contextFactory.CreateDbContext();
        var tran = dc.Database.BeginTransaction();
        try
        {
            var log = (from a in dc.MemoApprovalLogs
                where a.MemoApprovalLogId == logId
                select a).FirstOrDefault();
            byte actionId = 5;

            if (toApproverId == null)
                return Task.FromResult("No destination approver is selected");
            var sameUser = (from a in dc.MemoApprovalLogs
                            where a.MemoApprovalLogId == logId &&
                            a.ToApprover.MemoApproverUserId == userId
                            select a).Any();
            if (log != null)
            {
                var newLog = new MemoApprovalLog
                {
                    MemoId = log.MemoId,
                    FromApproverId = log.ToApproverId ?? 0,
                    ToApproverId = toApproverId, // Use calculated value
                    ApprovalActionsId = actionId,
                    ActionDate = DateTime.Now,
                    PrevReplyLogId = log.MemoApprovalLogId,
                    IsForwarded = true,
                    Comments = comments,
                    //DelegatedUserId = log.ToApprover.MemoApproverUserId == userId ? null : userId
                    DelegatedUserId = sameUser ? null : userId

                };

                dc.MemoApprovalLogs.Add(newLog);
                dc.SaveChanges();

                log.IsForwarded = true;
                log.ReplyLogId = newLog.MemoApprovalLogId;
                //log.LastActionByUserId = userId;
                dc.SaveChanges();
            }

            dc.SaveChanges();
            tran.Commit();
            return Task.FromResult("OK");
        }
        catch (Exception e)
        {
            tran.Rollback();
            Console.WriteLine(e);
            throw;
        }
    }

    public async Task<List<MemoAttachmentDto>> GetMemoAttachments(int logId)
    {
        var dc = contextFactory.CreateDbContext();
        var memo = await (from a in dc.MemoApprovalLogs
            where a.MemoApprovalLogId == logId
            select a.MemoId).FirstOrDefaultAsync();

        if (memo == 0) return new List<MemoAttachmentDto>();

        var q = await (from a in dc.MemoAttachments
            where a.MemoId == memo
                  && a.MemoAttachmentIsDel == false
                  && a.MemoAttachmentIsActive == true
            select new MemoAttachmentDto
            {
                AttachmentTypeId = a.AttachmentTypeId,
                Name = a.MemoAttachmentDocName,
                Path = AppDataService.ConvertToUrl(a.MemoAttachmentFilePath),
                Type = a.MemoAttachmentDocType,
                Size = a.MemoAttachmentDocSizeMb.ToString(),
                AttachmentType = a.AttachmentType.AttachmentTypeTitle,
                Description = a.MemoAttachmentDescription
            }).ToListAsync();

        return q;
    }

    public async Task<string> PerformSkip(int logId, string comments, string userId)
    {
        var dc = contextFactory.CreateDbContext();
        using var transaction = dc.Database.BeginTransaction();
        try
        {
            var log = await dc.MemoApprovalLogs
                .FirstOrDefaultAsync(c => c.MemoApprovalLogId == logId);

            if (log == null)
                return "Log entry not found";

            // Get next approver
            var nextApproverId = GetNextApproverId(log.MemoId, log.ToApproverId ?? 0);

            if (nextApproverId == null)
                return "Next approver not found";

            // Mark current approver as skipped
            var currentApprover = await dc.MemoApprovers
                .FirstOrDefaultAsync(a => a.MemoId == log.MemoId &&
                                        a.MemoApproverId == log.ToApproverId);

            if (currentApprover != null)
            {
                currentApprover.MemoApproverSkipped = true;
            }

            var sameUser = (from a in dc.MemoApprovalLogs
                            where a.MemoApprovalLogId == logId &&
                            a.ToApprover.MemoApproverUserId == userId
                            select a).Any();

            // Create new log entry
            var newLog = new MemoApprovalLog
            {
                MemoId = log.MemoId,
                FromApproverId = log.ToApproverId ?? 0,
                ToApproverId = nextApproverId,
                ApprovalActionsId = (byte)ApprovalActionEnum.Skip,
                ActionDate = DateTime.Now,
                PrevReplyLogId = log.MemoApprovalLogId,
                IsForwarded = true,
                Comments = comments,
                //DelegatedUserId = log.ToApprover.MemoApproverUserId == userId ? null : userId
                DelegatedUserId = sameUser ? null : userId
            };

            dc.MemoApprovalLogs.Add(newLog);
            await dc.SaveChangesAsync();

            // Update previous log
            log.IsForwarded = true;
            log.ReplyLogId = newLog.MemoApprovalLogId;
            //log.LastActionByUserId = userId;
            await dc.SaveChangesAsync();

            await transaction.CommitAsync();
            return "OK";
        }
        catch (Exception e)
        {
            await transaction.RollbackAsync();
            return e.Message;
        }
    }

    public async Task SendEmailToNextApprover(int logId, string action)
    {
        using var dc = contextFactory.CreateDbContext(); // Ensure proper disposal of DbContext
        try
        {
            // Get the current log entry
            var log = await dc.MemoApprovalLogs
                .FirstOrDefaultAsync(c => c.MemoApprovalLogId == logId);

            if (log == null) return;

            // Get the memo details
            var memo = await dc.Memos
                .FirstOrDefaultAsync(m => m.MemoId == log.MemoId);

            if (memo == null) return;

            // Determine the next approver's ID
            int? nextApproverId = action.ToLower() switch
            {
                "approve" or "approved" => GetNextApproverId(log.MemoId, log.ToApproverId ?? 0),
                "query" or "object" => log.DraftToUserId,
                _ => null
            };

            if (nextApproverId == null) return;

            // Get the approver's details
            var approver = await dc.MemoApprovers
                .FirstOrDefaultAsync(a => a.MemoApproverId == nextApproverId);

            if (approver == null) return;

            // Get the user's email
            var user = await dc.Users
                .FirstOrDefaultAsync(u => u.UserId.ToLower() == approver.MemoApproverUserId.ToLower());

            if (user == null || string.IsNullOrEmpty(user.Email)) return;

            // Create email subject and body
            string subject = $"Memo {memo.MemoCode} - Action Required: {action}";

            string body = $@"Dear {approver.MemoApproverUserName},

A memo requires your attention in the PS Smart Memo system.

Memo Code: {memo.MemoCode}
Memo Title: {memo.MemoTitle}
Action Required: {action}

Comments: {log.DraftComments}

Please log in to the PS Smart Memo system to review and take appropriate action.

This is an automated message. Please do not reply to this email.";

            // Send the email
            await corporateService.SendEmailAsync(user.Email, subject, body);
        }
        catch (Exception ex)
        {
            var msg = ex.Message;
            if(ex.InnerException!=null)
                msg += ex.InnerException.Message;
            Console.WriteLine($"Error sending email to next approver: {msg}");
        }
    }
    
    public Task<int> GetMemoObjectLogId(int memoId)
    {
        var dc = contextFactory.CreateDbContext();
        var q = (from a in dc.MemoApprovalLogs
                 where a.MemoId == memoId &&
                 a.ApprovalActions.ApprovalActionTitle.ToLower() == "object" &&
                 a.ReplyLogId == null
                 select a).FirstOrDefault();
        if(q!=null)
        {
            return Task.FromResult(q.MemoApprovalLogId);
        }
        else
        {
            return Task.FromResult(0);
        }
    }
    public Task<bool> IsMemoAlreadyForwarded(int logId)
    {
        var dc = contextFactory.CreateDbContext();
        var q = (from a in dc.MemoApprovalLogs
                 where a.MemoApprovalLogId == logId &&
                 a.ReplyLogId != null
                 select a).Any();
        return Task.FromResult(q);
    }
}
