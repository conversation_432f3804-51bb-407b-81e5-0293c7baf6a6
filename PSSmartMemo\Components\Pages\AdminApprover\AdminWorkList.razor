@page "/admin/approver-worklist"

@inject WorklistDataService Service
@inject AdminDataService AdminService
@attribute [Authorize]
@rendermode InteractiveServer
@using FilterType = Syncfusion.Blazor.Grids.FilterType

<SfBreadcrumb class="mb-2">
    <BreadcrumbItems>
        <BreadcrumbItem IconCss="e-icons e-home" Text="Home" Url="/"></BreadcrumbItem>
        <BreadcrumbItem Text="Admin" Url=""></BreadcrumbItem>
        <BreadcrumbItem Text="Approver Worklist" Url=""></BreadcrumbItem>
    </BreadcrumbItems>
</SfBreadcrumb>

<div class="mb-2" style="display: flex; gap: 10px; align-items: center;justify-content: space-between">
    <MudText Typo="Typo.h5">Admin - Approver Worklist</MudText>
</div>

<div class="mb-3">
    <div class="row">
        <div class="col-md">
            <SfDropDownList DataSource="@allUsers"
                            Placeholder="Select Approver"
                            @bind-Value="selectedUserId"
                            TValue="string"
                            TItem="UserDTO"
                            AllowFiltering="true"
                            FilterType="Syncfusion.Blazor.DropDowns.FilterType.Contains"
                            FloatLabelType="FloatLabelType.Always">
                <DropDownListFieldSettings Value="@nameof(UserDTO.UserId)"
                                           Text="@nameof(UserDTO.Name)">
                </DropDownListFieldSettings>
                <DropDownListEvents TValue="string" TItem="UserDTO" ValueChange="OnApproverChanged"></DropDownListEvents>
            </SfDropDownList>
        </div>
        <div class="col-md-2">
            <MudButton Variant="Variant.Filled"
                       Color="Color.Primary"
                       OnClick="LoadApproverWorklist"
                       Disabled="string.IsNullOrEmpty(selectedUserId)">
                Load Worklist
            </MudButton>
        </div>
    </div>
</div>

@if (!string.IsNullOrEmpty(selectedUserId) && worklist.Any())
{
    <div class="mb-2">
        <MudText Typo="Typo.h6">Worklist for: @selectedApproverName</MudText>
    </div>

    <SfGrid DataSource="@worklist"
            AllowPaging="true"
            AllowSorting="true"
            AllowFiltering="true"
            Height="600px">
        <GridPageSettings PageSize="20"></GridPageSettings>
        <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
        <GridColumns>
            <GridColumn Field="@nameof(MemoDto.MemoCode)" HeaderText="Memo Code" AutoFit="true"></GridColumn>
            <GridColumn Field="@nameof(MemoDto.MemoTitle)" HeaderText="Title" AutoFit="true"></GridColumn>
            <GridColumn Field="@nameof(MemoDto.MemoTypeStr)" HeaderText="Type" AutoFit="true"></GridColumn>
            <GridColumn Field="@nameof(MemoDto.MemoCreatedBy)" HeaderText="Created By" AutoFit="true"></GridColumn>
            <GridColumn Field="@nameof(MemoDto.MemoCreatedDate)" HeaderText="Created Date" AutoFit="true" Format="d"></GridColumn>
            <GridColumn Field="@nameof(MemoDto.LastAction)" HeaderText="Last Action" AutoFit="true"></GridColumn>
            <GridColumn Field="@nameof(MemoDto.PendingSince)" HeaderText="Pending Since" AutoFit="true"></GridColumn>
            <GridColumn HeaderText="Actions" AutoFit="true">
                <Template>
                    @{
                        var memo = context as MemoDto;
                        var lnk = $"/admin/approver-viewmemo/{memo.MemoApprovalLogId}";
                        var trg = $"am-{memo.MemoApprovalLogId}";
                            <a href="@lnk" target="@trg" style="
                                display: inline-block;
                                padding: 6px 18px;
                                background: linear-gradient(90deg, #007bff 0%, #0056b3 100%);
                                color: #fff;
                                border: none;
                                border-radius: 4px;
                                font-size: 0.95rem;
                                font-weight: 500;
                                text-decoration: none;
                                box-shadow: 0 2px 6px rgba(0,0,0,0.08);
                                transition: background 0.2s, box-shadow 0.2s;
                                cursor: pointer;
                            }
                            :hover {
                                background: linear-gradient(90deg, #0056b3 0%, #007bff 100%);
                                color: #fff;
                                text-decoration: none;
                                box-shadow: 0 4px 12px rgba(0,0,0,0.12);
                            }">View</a>
                    }
                </Template>
            </GridColumn>
        </GridColumns>
    </SfGrid>
}
else if (!string.IsNullOrEmpty(selectedUserId) && !worklist.Any())
{
    <MudAlert Severity="Severity.Info">No pending items found for the selected approver.</MudAlert>
}
else
{
    <MudAlert Severity="Severity.Normal">Please select an approver to view their worklist.</MudAlert>
}

@code {
    [CascadingParameter] public Task<AuthenticationState>? AuthState { get; set; }

    private string currentUserId = "";
    private string selectedUserId = "";
    private string selectedApproverName = "";
    private List<UserDTO> allUsers = new();
    private List<MemoDto> worklist = new();

    protected override async Task OnInitializedAsync()
    {
        if (AuthState != null)
        {
            var authState = await AuthState;
            if (authState.User.Identity?.IsAuthenticated ?? false)
            {
                currentUserId = authState.User.Identity.Name!;
            }
            else
            {
                NavMgr.NavigateTo("/");
                return;
            }
        }

        // Load all active users for the dropdown
        //allUsers = await AdminService.GetAllUsers();
        allUsers = await AdminService.GetAllActiveUsers();
    }

    private async Task OnApproverChanged(ChangeEventArgs<string, UserDTO> args)
    {
        selectedUserId = args.Value ?? "";
        if (!string.IsNullOrEmpty(selectedUserId))
        {
            var selectedUser = allUsers.FirstOrDefault(u => u.UserId == selectedUserId);
            selectedApproverName = selectedUser?.Name ?? "";
        }
        else
        {
            selectedApproverName = "";
            worklist.Clear();
        }

        StateHasChanged();
    }

    private async Task LoadApproverWorklist()
    {
        if (string.IsNullOrEmpty(selectedUserId))
            return;

        try
        {
            worklist = await Service.GetMyWorkList(selectedUserId);
            StateHasChanged();
        }
        catch (Exception ex)
        {
            // Handle error - could add toast notification here
            Console.WriteLine($"Error loading worklist: {ex.Message}");
        }
    }

    private void ViewMemo(MemoDto memo)
    {
        NavMgr.NavigateTo($"/admin/approver-viewmemo/{memo.MemoApprovalLogId}");
    }

}