﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;

namespace PSSmartMemo.Model;

public partial class ApplicationDbContext : DbContext
{
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
        : base(options)
    {
    }

    public virtual DbSet<ApprovalAction> ApprovalActions { get; set; }

    public virtual DbSet<AttachmentType> AttachmentTypes { get; set; }

    public virtual DbSet<Category> Categories { get; set; }

    public virtual DbSet<Delegation> Delegations { get; set; }

    public virtual DbSet<EmpLocationTiming> EmpLocationTimings { get; set; }

    public virtual DbSet<FormStatus> FormStatuses { get; set; }

    public virtual DbSet<FormType> FormTypes { get; set; }

    public virtual DbSet<FormTypeStatus> FormTypeStatuses { get; set; }

    public virtual DbSet<Location> Locations { get; set; }

    public virtual DbSet<Memo> Memos { get; set; }

    public virtual DbSet<MemoApprovalLog> MemoApprovalLogs { get; set; }

    public virtual DbSet<MemoApprover> MemoApprovers { get; set; }

    public virtual DbSet<MemoApproverRole> MemoApproverRoles { get; set; }

    public virtual DbSet<MemoAttachment> MemoAttachments { get; set; }

    public virtual DbSet<MemoSection> MemoSections { get; set; }

    public virtual DbSet<MemoStatus> MemoStatuses { get; set; }

    public virtual DbSet<MemoTemplate> MemoTemplates { get; set; }

    public virtual DbSet<MemoTemplateApprover> MemoTemplateApprovers { get; set; }

    public virtual DbSet<MemoTemplateSection> MemoTemplateSections { get; set; }

    public virtual DbSet<MemoTemplateUserRoleAssign> MemoTemplateUserRoleAssigns { get; set; }

    public virtual DbSet<MemoType> MemoTypes { get; set; }

    public virtual DbSet<Menu> Menus { get; set; }

    public virtual DbSet<Module> Modules { get; set; }

    public virtual DbSet<OfficeLocation> OfficeLocations { get; set; }

    public virtual DbSet<ReportUsersMemoTemplate> ReportUsersMemoTemplates { get; set; }

    public virtual DbSet<Role> Roles { get; set; }

    public virtual DbSet<RoleMenu> RoleMenus { get; set; }

    public virtual DbSet<ShiftLocation> ShiftLocations { get; set; }

    public virtual DbSet<SubCategory> SubCategories { get; set; }

    public virtual DbSet<TemplateStatus> TemplateStatuses { get; set; }

    public virtual DbSet<User> Users { get; set; }

    public virtual DbSet<UserRole> UserRoles { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<ApprovalAction>(entity =>
        {
            entity.HasKey(e => e.ApprovalActionId).HasName("PK__Approval__6E6B032C83187955");

            entity.HasIndex(e => e.ApprovalActionTitle, "UQ__Approval__FD6984CAC4A52854").IsUnique();

            entity.Property(e => e.ApprovalActionTitle)
                .IsRequired()
                .HasMaxLength(50)
                .IsUnicode(false);
        });

        modelBuilder.Entity<AttachmentType>(entity =>
        {
            entity.HasKey(e => e.AttachmentTypeId).HasName("PK__Attachme__5C63AB642B0CBA32");

            entity.Property(e => e.AttachmentTypeTitle)
                .IsRequired()
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.Description)
                .HasMaxLength(1000)
                .IsUnicode(false);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedBy)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.HasOne(d => d.MemoType).WithMany(p => p.AttachmentTypes)
                .HasForeignKey(d => d.MemoTypeId)
                .HasConstraintName("FK__Attachmen__MemoT__5CA1C101");
        });

        modelBuilder.Entity<Category>(entity =>
        {
            entity.Property(e => e.CategoryTitle).HasMaxLength(200);
        });

        modelBuilder.Entity<Delegation>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Delegati__3214EC07B38E55A1");

            entity.Property(e => e.Comments)
                .HasMaxLength(1000)
                .IsUnicode(false);
            entity.Property(e => e.CraatedBy)
                .IsRequired()
                .HasMaxLength(300)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.FromUserId)
                .IsRequired()
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedBy)
                .HasMaxLength(300)
                .IsUnicode(false);
            entity.Property(e => e.ToUserId)
                .IsRequired()
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.Type)
                .HasMaxLength(100)
                .IsUnicode(false);

            entity.HasOne(d => d.FromUser).WithMany(p => p.DelegationFromUsers)
                .HasPrincipalKey(p => p.UserId)
                .HasForeignKey(d => d.FromUserId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__Delegatio__FromU__373B3228");

            entity.HasOne(d => d.ToUser).WithMany(p => p.DelegationToUsers)
                .HasPrincipalKey(p => p.UserId)
                .HasForeignKey(d => d.ToUserId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__Delegatio__ToUse__382F5661");
        });

        modelBuilder.Entity<EmpLocationTiming>(entity =>
        {
            entity.ToTable("EmpLocationTiming");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.EmpId).HasColumnName("empId");
            entity.Property(e => e.FromTime).HasColumnType("datetime");
            entity.Property(e => e.ToTime).HasColumnType("datetime");
        });

        modelBuilder.Entity<FormStatus>(entity =>
        {
            entity.HasKey(e => e.FormStatusId).HasName("PK_formStatuses");

            entity.Property(e => e.FormStatusId).HasColumnName("formStatusId");
            entity.Property(e => e.FormId).HasColumnName("formId");
            entity.Property(e => e.FormStatusCreatedBy)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("formStatusCreatedBy");
            entity.Property(e => e.FormStatusCreatedDate)
                .HasColumnType("datetime")
                .HasColumnName("formStatusCreatedDate");
            entity.Property(e => e.FormStatusModifiedBy)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("formStatusModifiedBy");
            entity.Property(e => e.FormStatusModifiedDate)
                .HasColumnType("datetime")
                .HasColumnName("formStatusModifiedDate");
            entity.Property(e => e.FormStatusUserEmail)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("formStatusUserEmail");
            entity.Property(e => e.FormStatusUserId)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("formStatusUserId");
            entity.Property(e => e.FormStatusUserRemarks)
                .HasMaxLength(500)
                .IsUnicode(false)
                .HasColumnName("formStatusUserRemarks");
            entity.Property(e => e.FormTypeId).HasColumnName("formTypeId");
            entity.Property(e => e.StatusId).HasColumnName("statusId");

            entity.HasOne(d => d.FormType).WithMany(p => p.FormStatuses)
                .HasForeignKey(d => d.FormTypeId)
                .HasConstraintName("FK__FormStatu__formT__1DB06A4F");

            entity.HasOne(d => d.Status).WithMany(p => p.InverseStatus)
                .HasForeignKey(d => d.StatusId)
                .HasConstraintName("FK__FormStatu__statu__1EA48E88");
        });

        modelBuilder.Entity<FormType>(entity =>
        {
            entity.Property(e => e.FormTypeId).HasColumnName("formTypeId");
            entity.Property(e => e.FormTypeCode)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("formTypeCode");
            entity.Property(e => e.FormTypeCreatedBy)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("formTypeCreatedBy");
            entity.Property(e => e.FormTypeCreatedDate)
                .HasColumnType("datetime")
                .HasColumnName("formTypeCreatedDate");
            entity.Property(e => e.FormTypeModifiedBy)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("formTypeModifiedBy");
            entity.Property(e => e.FormTypeModifiedDate)
                .HasColumnType("datetime")
                .HasColumnName("formTypeModifiedDate");
            entity.Property(e => e.FormTypeTitle)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("formTypeTitle");
        });

        modelBuilder.Entity<FormTypeStatus>(entity =>
        {
            entity.HasKey(e => e.FormTypeStatusId).HasName("PK_formTypeWorkflows");

            entity.Property(e => e.FormTypeStatusId).HasColumnName("formTypeStatusId");
            entity.Property(e => e.FormTypeId).HasColumnName("formTypeId");
            entity.Property(e => e.FormTypeStatusCode)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("formTypeStatusCode");
            entity.Property(e => e.FormTypeStatusCreatedBy)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("formTypeStatusCreatedBy");
            entity.Property(e => e.FormTypeStatusCreatedDate)
                .HasColumnType("datetime")
                .HasColumnName("formTypeStatusCreatedDate");
            entity.Property(e => e.FormTypeStatusModifiedBy)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("formTypeStatusModifiedBy");
            entity.Property(e => e.FormTypeStatusModifiedDate)
                .HasColumnType("datetime")
                .HasColumnName("formTypeStatusModifiedDate");
            entity.Property(e => e.FormTypeStatusTitle)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("formTypeStatusTitle");

            entity.HasOne(d => d.FormType).WithMany(p => p.FormTypeStatuses)
                .HasForeignKey(d => d.FormTypeId)
                .HasConstraintName("FK__FormTypeS__formT__1CBC4616");
        });

        modelBuilder.Entity<Location>(entity =>
        {
            entity.Property(e => e.EmployeeShiftCode)
                .HasMaxLength(255)
                .HasColumnName("Employee_Shift_Code");
            entity.Property(e => e.LocationCode)
                .HasMaxLength(255)
                .HasColumnName("Location_Code");
            entity.Property(e => e.LocationTxt)
                .HasMaxLength(255)
                .HasColumnName("Location_TXT");
            entity.Property(e => e.ShiftEnd).HasColumnName("Shift_End");
            entity.Property(e => e.ShiftStart).HasColumnName("Shift_Start");
        });

        modelBuilder.Entity<Memo>(entity =>
        {
            entity.Property(e => e.MemoId).HasColumnName("memoId");
            entity.Property(e => e.CurrentStep)
                .HasDefaultValue(1)
                .HasColumnName("currentStep");
            entity.Property(e => e.DepartmentCode)
                .HasMaxLength(15)
                .IsUnicode(false);
            entity.Property(e => e.FormTypeId).HasColumnName("formTypeId");
            entity.Property(e => e.LastAction)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.MemoCode)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("memoCode");
            entity.Property(e => e.MemoCreatedBy)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("memoCreatedBy");
            entity.Property(e => e.MemoCreatedDate)
                .HasColumnType("datetime")
                .HasColumnName("memoCreatedDate");
            entity.Property(e => e.MemoDepartment)
                .HasMaxLength(200)
                .IsUnicode(false)
                .HasColumnName("memoDepartment");
            entity.Property(e => e.MemoDivision)
                .HasMaxLength(200)
                .IsUnicode(false)
                .HasColumnName("memoDivision");
            entity.Property(e => e.MemoIsActive)
                .HasDefaultValue(true)
                .HasColumnName("memoIsActive");
            entity.Property(e => e.MemoIsDel).HasColumnName("memoIsDel");
            entity.Property(e => e.MemoModifiedBy)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("memoModifiedBy");
            entity.Property(e => e.MemoModifiedDate)
                .HasColumnType("datetime")
                .HasColumnName("memoModifiedDate");
            entity.Property(e => e.MemoStatus)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("memoStatus");
            entity.Property(e => e.MemoTemplateId).HasColumnName("memoTemplateId");
            entity.Property(e => e.MemoTitle)
                .HasMaxLength(500)
                .IsUnicode(false)
                .HasColumnName("memoTitle");
            entity.Property(e => e.MemoTypeId).HasColumnName("memoTypeId");
            entity.Property(e => e.NextActivity)
                .HasMaxLength(300)
                .IsUnicode(false);

            entity.HasOne(d => d.FormType).WithMany(p => p.Memos)
                .HasForeignKey(d => d.FormTypeId)
                .HasConstraintName("FK__Memos__formTypeI__0C85DE4D");

            entity.HasOne(d => d.MemoStatusNavigation).WithMany(p => p.Memos)
                .HasForeignKey(d => d.MemoStatusId)
                .HasConstraintName("FK__Memos__MemoStatu__0D7A0286");

            entity.HasOne(d => d.MemoTemplate).WithMany(p => p.Memos)
                .HasForeignKey(d => d.MemoTemplateId)
                .HasConstraintName("FK__Memos__memoTempl__0E6E26BF");

            entity.HasOne(d => d.MemoType).WithMany(p => p.Memos)
                .HasForeignKey(d => d.MemoTypeId)
                .HasConstraintName("FK__Memos__memoTypeI__0F624AF8");
        });

        modelBuilder.Entity<MemoApprovalLog>(entity =>
        {
            entity.HasKey(e => e.MemoApprovalLogId).HasName("PK__MemoAppr__824336434CA33940");

            entity.Property(e => e.ActionDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.DelegatedUserId)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.ToApproverUserId)
                .HasMaxLength(300)
                .IsUnicode(false);

            entity.HasOne(d => d.ApprovalActions).WithMany(p => p.MemoApprovalLogs)
                .HasForeignKey(d => d.ApprovalActionsId)
                .HasConstraintName("FK__MemoAppro__Appro__17036CC0");

            entity.HasOne(d => d.DelegatedUser).WithMany(p => p.MemoApprovalLogs)
                .HasPrincipalKey(p => p.UserId)
                .HasForeignKey(d => d.DelegatedUserId)
                .HasConstraintName("FK__MemoAppro__Deleg__39237A9A");

            entity.HasOne(d => d.FromApprover).WithMany(p => p.MemoApprovalLogFromApprovers)
                .HasForeignKey(d => d.FromApproverId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__MemoAppro__FromA__17F790F9");

            entity.HasOne(d => d.Memo).WithMany(p => p.MemoApprovalLogs)
                .HasForeignKey(d => d.MemoId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__MemoAppro__MemoI__18EBB532");

            entity.HasOne(d => d.PrevReplyLog).WithMany(p => p.InversePrevReplyLog)
                .HasForeignKey(d => d.PrevReplyLogId)
                .HasConstraintName("FK__MemoAppro__PrevR__19DFD96B");

            entity.HasOne(d => d.ReplyLog).WithMany(p => p.InverseReplyLog)
                .HasForeignKey(d => d.ReplyLogId)
                .HasConstraintName("FK__MemoAppro__Reply__1AD3FDA4");

            entity.HasOne(d => d.ToApprover).WithMany(p => p.MemoApprovalLogToApprovers)
                .HasForeignKey(d => d.ToApproverId)
                .HasConstraintName("FK__MemoAppro__ToApp__1BC821DD");
        });

        modelBuilder.Entity<MemoApprover>(entity =>
        {
            entity.HasKey(e => e.MemoApproverId);

            entity.Property(e => e.Department)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.Designation)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.Designation2)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.MemoAllowType)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.MemoApproverCode)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.MemoApproverCreatedBy)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.MemoApproverCreatedDate).HasColumnType("datetime");
            entity.Property(e => e.MemoApproverDescription)
                .HasMaxLength(500)
                .IsUnicode(false);
            entity.Property(e => e.MemoApproverIsActive).HasDefaultValue(true);
            entity.Property(e => e.MemoApproverModifiedBy)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.MemoApproverModifiedDate).HasColumnType("datetime");
            entity.Property(e => e.MemoApproverTitle)
                .HasMaxLength(500)
                .IsUnicode(false);
            entity.Property(e => e.MemoApproverUserDetail)
                .HasMaxLength(500)
                .IsUnicode(false);
            entity.Property(e => e.MemoApproverUserEmail)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.MemoApproverUserId)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.MemoApproverUserName)
                .HasMaxLength(250)
                .IsUnicode(false);

            entity.HasOne(d => d.MemoApproverRole).WithMany(p => p.MemoApprovers)
                .HasForeignKey(d => d.MemoApproverRoleId)
                .HasConstraintName("FK__MemoAppro__MemoA__151B244E");

            entity.HasOne(d => d.Memo).WithMany(p => p.MemoApprovers)
                .HasForeignKey(d => d.MemoId)
                .HasConstraintName("FK__MemoAppro__MemoI__160F4887");
        });

        modelBuilder.Entity<MemoApproverRole>(entity =>
        {
            entity.Property(e => e.MemoApproverRoleId).HasColumnName("memoApproverRoleId");
            entity.Property(e => e.MemoApproverHcmcode)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("MemoApproverHCMCode");
            entity.Property(e => e.MemoApproverRoleCreatedBy)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("memoApproverRoleCreatedBy");
            entity.Property(e => e.MemoApproverRoleCreatedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime")
                .HasColumnName("memoApproverRoleCreatedDate");
            entity.Property(e => e.MemoApproverRoleDesc)
                .HasMaxLength(500)
                .IsUnicode(false)
                .HasColumnName("memoApproverRoleDesc");
            entity.Property(e => e.MemoApproverRoleIsActive)
                .HasDefaultValue(true)
                .HasColumnName("memoApproverRoleIsActive");
            entity.Property(e => e.MemoApproverRoleIsDel).HasColumnName("memoApproverRoleIsDel");
            entity.Property(e => e.MemoApproverRoleModifiedBy)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("memoApproverRoleModifiedBy");
            entity.Property(e => e.MemoApproverRoleModifiedDate)
                .HasColumnType("datetime")
                .HasColumnName("memoApproverRoleModifiedDate");
            entity.Property(e => e.MemoApproverRoleTitle)
                .HasMaxLength(500)
                .IsUnicode(false)
                .HasColumnName("memoApproverRoleTitle");
            entity.Property(e => e.MemoApproverRoleType)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("Dynamic | Fixed | Generic")
                .HasColumnName("memoApproverRoleType");
            entity.Property(e => e.MemoApproverRoleUserCode)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("memoApproverRoleUserCode");
            entity.Property(e => e.MemoApproverRoleUserDept)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("memoApproverRoleUserDept");
            entity.Property(e => e.MemoApproverRoleUserDesg)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("memoApproverRoleUserDesg");
            entity.Property(e => e.MemoApproverRoleUserEmail)
                .HasMaxLength(300)
                .IsUnicode(false)
                .HasColumnName("memoApproverRoleUserEmail");
            entity.Property(e => e.MemoApproverRoleUserId)
                .HasMaxLength(300)
                .IsUnicode(false)
                .HasColumnName("memoApproverRoleUserId");
            entity.Property(e => e.MemoApproverRoleUserName)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("memoApproverRoleUserName");
        });

        modelBuilder.Entity<MemoAttachment>(entity =>
        {
            entity.Property(e => e.MemoAttachmentId).HasColumnName("memoAttachmentId");
            entity.Property(e => e.MemoAttachmentCode)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("memoAttachmentCode");
            entity.Property(e => e.MemoAttachmentCreatedBy)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("memoAttachmentCreatedBy");
            entity.Property(e => e.MemoAttachmentCreatedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime")
                .HasColumnName("memoAttachmentCreatedDate");
            entity.Property(e => e.MemoAttachmentDescription)
                .HasMaxLength(1000)
                .IsUnicode(false)
                .HasColumnName("memoAttachmentDescription");
            entity.Property(e => e.MemoAttachmentDocName)
                .HasMaxLength(500)
                .IsUnicode(false)
                .HasColumnName("memoAttachmentDocName");
            entity.Property(e => e.MemoAttachmentDocSizeMb).HasColumnName("memoAttachmentDocSizeMB");
            entity.Property(e => e.MemoAttachmentDocType)
                .HasMaxLength(500)
                .IsUnicode(false)
                .HasColumnName("memoAttachmentDocType");
            entity.Property(e => e.MemoAttachmentFilePath)
                .HasMaxLength(2000)
                .IsUnicode(false)
                .HasColumnName("memoAttachmentFilePath");
            entity.Property(e => e.MemoAttachmentIsActive)
                .HasDefaultValue(true)
                .HasColumnName("memoAttachmentIsActive");
            entity.Property(e => e.MemoAttachmentIsDel).HasColumnName("memoAttachmentIsDel");
            entity.Property(e => e.MemoAttachmentKeywords)
                .HasMaxLength(1000)
                .IsUnicode(false)
                .HasColumnName("memoAttachmentKeywords");
            entity.Property(e => e.MemoAttachmentModifiedBy)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("memoAttachmentModifiedBy");
            entity.Property(e => e.MemoAttachmentModifiedDate)
                .HasColumnType("datetime")
                .HasColumnName("memoAttachmentModifiedDate");
            entity.Property(e => e.MemoAttachmentSortOrder).HasColumnName("memoAttachmentSortOrder");
            entity.Property(e => e.MemoAttachmentTitle)
                .HasMaxLength(500)
                .IsUnicode(false)
                .HasColumnName("memoAttachmentTitle");
            entity.Property(e => e.MemoId).HasColumnName("memoId");

            entity.HasOne(d => d.AttachmentType).WithMany(p => p.MemoAttachments)
                .HasForeignKey(d => d.AttachmentTypeId)
                .HasConstraintName("FK__MemoAttac__Attac__2739D489");

            entity.HasOne(d => d.Memo).WithMany(p => p.MemoAttachments)
                .HasForeignKey(d => d.MemoId)
                .HasConstraintName("FK__MemoAttac__memoI__123EB7A3");
        });

        modelBuilder.Entity<MemoSection>(entity =>
        {
            entity.Property(e => e.MemoSectionId)
                .HasDefaultValueSql("(newid())")
                .HasColumnName("memoSectionId");
            entity.Property(e => e.MemoId).HasColumnName("memoId");
            entity.Property(e => e.MemoSectionCode)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("memoSectionCode");
            entity.Property(e => e.MemoSectionContentHtml)
                .HasColumnType("ntext")
                .HasColumnName("memoSectionContentHTML");
            entity.Property(e => e.MemoSectionContentText)
                .HasColumnType("ntext")
                .HasColumnName("memoSectionContentText");
            entity.Property(e => e.MemoSectionCreatedBy)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("memoSectionCreatedBy");
            entity.Property(e => e.MemoSectionCreatedDate)
                .HasColumnType("datetime")
                .HasColumnName("memoSectionCreatedDate");
            entity.Property(e => e.MemoSectionIgnored).HasColumnName("memoSectionIgnored");
            entity.Property(e => e.MemoSectionIsActive)
                .HasDefaultValue(true)
                .HasColumnName("memoSectionIsActive");
            entity.Property(e => e.MemoSectionIsDel).HasColumnName("memoSectionIsDel");
            entity.Property(e => e.MemoSectionModifiedBy)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("memoSectionModifiedBy");
            entity.Property(e => e.MemoSectionModifiedDate)
                .HasColumnType("datetime")
                .HasColumnName("memoSectionModifiedDate");
            entity.Property(e => e.MemoSectionSortOrder).HasColumnName("memoSectionSortOrder");
            entity.Property(e => e.MemoSectionTitle)
                .HasMaxLength(250)
                .IsUnicode(false)
                .HasColumnName("memoSectionTitle");
            entity.Property(e => e.MemoTemplateSectionId).HasColumnName("memoTemplateSectionId");

            entity.HasOne(d => d.Memo).WithMany(p => p.MemoSections)
                .HasForeignKey(d => d.MemoId)
                .HasConstraintName("FK__MemoSecti__memoI__10566F31");

            entity.HasOne(d => d.MemoTemplateSection).WithMany(p => p.MemoSections)
                .HasForeignKey(d => d.MemoTemplateSectionId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__MemoSecti__memoT__114A936A");
        });

        modelBuilder.Entity<MemoStatus>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_MemoStatuses_Id");

            entity.Property(e => e.MemoStatusTitle)
                .IsRequired()
                .HasMaxLength(100)
                .IsUnicode(false);
        });

        modelBuilder.Entity<MemoTemplate>(entity =>
        {
            entity.HasKey(e => e.MemoTemplateId).HasName("PK__MemoTemp__F15442F4948A9320");

            entity.Property(e => e.MemoTemplateId).HasColumnName("memoTemplateId");
            entity.Property(e => e.FormTypeId).HasColumnName("formTypeId");
            entity.Property(e => e.MemoTemplateApproverCountAllowed)
                .HasDefaultValue(1)
                .HasColumnName("memoTemplateApproverCountAllowed");
            entity.Property(e => e.MemoTemplateAttachmentAllowed)
                .HasDefaultValue(true)
                .HasColumnName("memoTemplateAttachmentAllowed");
            entity.Property(e => e.MemoTemplateAttachmentFileCountAllowed)
                .HasColumnType("numeric(18, 0)")
                .HasColumnName("memoTemplateAttachmentFileCountAllowed");
            entity.Property(e => e.MemoTemplateAttachmentPerFileSizeMballowed).HasColumnName("memoTemplateAttachmentPerFileSizeMBAllowed");
            entity.Property(e => e.MemoTemplateCode)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("memoTemplateCode");
            entity.Property(e => e.MemoTemplateCreatedBy)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("memoTemplateCreatedBy");
            entity.Property(e => e.MemoTemplateCreatedDate)
                .HasColumnType("datetime")
                .HasColumnName("memoTemplateCreatedDate");
            entity.Property(e => e.MemoTemplateIsActive)
                .HasDefaultValue(true)
                .HasColumnName("memoTemplateIsActive");
            entity.Property(e => e.MemoTemplateIsDel).HasColumnName("memoTemplateIsDel");
            entity.Property(e => e.MemoTemplateModifiedBy)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("memoTemplateModifiedBy");
            entity.Property(e => e.MemoTemplateModifiedDate)
                .HasColumnType("datetime")
                .HasColumnName("memoTemplateModifiedDate");
            entity.Property(e => e.MemoTemplatePrefixCode)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("memoTemplatePrefixCode");
            entity.Property(e => e.MemoTemplateStatus)
                .HasDefaultValue((byte)1)
                .HasColumnName("memoTemplateStatus");
            entity.Property(e => e.MemoTemplateTitle)
                .HasMaxLength(500)
                .IsUnicode(false)
                .HasColumnName("memoTemplateTitle");
            entity.Property(e => e.MemoTemplateVersion)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("memoTemplateVersion");
            entity.Property(e => e.MemoTypeId).HasColumnName("memoTypeId");

            entity.HasOne(d => d.FormType).WithMany(p => p.MemoTemplates)
                .HasForeignKey(d => d.FormTypeId)
                .HasConstraintName("FK__MemoTempl__formT__05D8E0BE");

            entity.HasOne(d => d.MemoType).WithMany(p => p.MemoTemplates)
                .HasForeignKey(d => d.MemoTypeId)
                .HasConstraintName("FK__MemoTempl__memoT__07C12930");
        });

        modelBuilder.Entity<MemoTemplateApprover>(entity =>
        {
            entity.HasKey(e => e.MemoTemplateApproverId).HasName("PK_memoTemplateApprovers");

            entity.Property(e => e.MemoTemplateApproverId)
                .HasDefaultValueSql("(newid())")
                .HasColumnName("memoTemplateApproverId");
            entity.Property(e => e.MemoApproverRoleId).HasColumnName("memoApproverRoleId");
            entity.Property(e => e.MemoTemplateApproverAllowType)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasDefaultValue("")
                .HasColumnName("memoTemplateApproverAllowType");
            entity.Property(e => e.MemoTemplateApproverCode)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("memoTemplateApproverCode");
            entity.Property(e => e.MemoTemplateApproverCreatedBy)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("memoTemplateApproverCreatedBy");
            entity.Property(e => e.MemoTemplateApproverCreatedDate)
                .HasColumnType("datetime")
                .HasColumnName("memoTemplateApproverCreatedDate");
            entity.Property(e => e.MemoTemplateApproverIsActive)
                .HasDefaultValue(true)
                .HasColumnName("memoTemplateApproverIsActive");
            entity.Property(e => e.MemoTemplateApproverIsDel).HasColumnName("memoTemplateApproverIsDel");
            entity.Property(e => e.MemoTemplateApproverModifiedBy)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("memoTemplateApproverModifiedBy");
            entity.Property(e => e.MemoTemplateApproverModifiedDate)
                .HasColumnType("datetime")
                .HasColumnName("memoTemplateApproverModifiedDate");
            entity.Property(e => e.MemoTemplateApproverSortOrder).HasColumnName("memoTemplateApproverSortOrder");
            entity.Property(e => e.MemoTemplateApproverTitle)
                .HasMaxLength(500)
                .IsUnicode(false)
                .HasColumnName("memoTemplateApproverTitle");
            entity.Property(e => e.MemoTemplateApproverUserDetail)
                .HasMaxLength(500)
                .IsUnicode(false)
                .HasColumnName("memoTemplateApproverUserDetail");
            entity.Property(e => e.MemoTemplateApproverUserEmail)
                .HasMaxLength(300)
                .IsUnicode(false)
                .HasColumnName("memoTemplateApproverUserEmail");
            entity.Property(e => e.MemoTemplateApproverUserId)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("memoTemplateApproverUserId");
            entity.Property(e => e.MemoTemplateApproverUserName)
                .HasMaxLength(250)
                .IsUnicode(false)
                .HasColumnName("memoTemplateApproverUserName");
            entity.Property(e => e.MemoTemplateId).HasColumnName("memoTemplateId");

            entity.HasOne(d => d.MemoApproverRole).WithMany(p => p.MemoTemplateApprovers)
                .HasForeignKey(d => d.MemoApproverRoleId)
                .HasConstraintName("FK__MemoTempl__memoA__0A9D95DB");

            entity.HasOne(d => d.MemoTemplate).WithMany(p => p.MemoTemplateApprovers)
                .HasForeignKey(d => d.MemoTemplateId)
                .HasConstraintName("FK__MemoTempl__memoT__0B91BA14");
        });

        modelBuilder.Entity<MemoTemplateSection>(entity =>
        {
            entity.Property(e => e.MemoTemplateSectionId)
                .HasDefaultValueSql("(newid())")
                .HasColumnName("memoTemplateSectionId");
            entity.Property(e => e.MemoTemplateId).HasColumnName("memoTemplateId");
            entity.Property(e => e.MemoTemplateSectionCode)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("memoTemplateSectionCode");
            entity.Property(e => e.MemoTemplateSectionContentHtml)
                .HasColumnType("ntext")
                .HasColumnName("memoTemplateSectionContentHTML");
            entity.Property(e => e.MemoTemplateSectionContentText)
                .HasColumnType("ntext")
                .HasColumnName("memoTemplateSectionContentText");
            entity.Property(e => e.MemoTemplateSectionCreatedBy)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("memoTemplateSectionCreatedBy");
            entity.Property(e => e.MemoTemplateSectionCreatedDate)
                .HasColumnType("datetime")
                .HasColumnName("memoTemplateSectionCreatedDate");
            entity.Property(e => e.MemoTemplateSectionDataType)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("memoTemplateSectionDataType");
            entity.Property(e => e.MemoTemplateSectionFormControl)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("memoTemplateSectionFormControl");
            entity.Property(e => e.MemoTemplateSectionIsActive)
                .HasDefaultValue(true)
                .HasColumnName("memoTemplateSectionIsActive");
            entity.Property(e => e.MemoTemplateSectionIsDel).HasColumnName("memoTemplateSectionIsDel");
            entity.Property(e => e.MemoTemplateSectionIsRequired)
                .HasDefaultValue(true)
                .HasColumnName("memoTemplateSectionIsRequired");
            entity.Property(e => e.MemoTemplateSectionIsWaterMark)
                .HasDefaultValue(true)
                .HasColumnName("memoTemplateSectionIsWaterMark");
            entity.Property(e => e.MemoTemplateSectionMendatoryType)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("memoTemplateSectionMendatoryType");
            entity.Property(e => e.MemoTemplateSectionModifiedBy)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("memoTemplateSectionModifiedBy");
            entity.Property(e => e.MemoTemplateSectionModifiedDate)
                .HasColumnType("datetime")
                .HasColumnName("memoTemplateSectionModifiedDate");
            entity.Property(e => e.MemoTemplateSectionSortOrder).HasColumnName("memoTemplateSectionSortOrder");
            entity.Property(e => e.MemoTemplateSectionTitle)
                .HasMaxLength(250)
                .IsUnicode(false)
                .HasColumnName("memoTemplateSectionTitle");
            entity.Property(e => e.MemoTemplateSectionType)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("memoTemplateSectionType");

            entity.HasOne(d => d.MemoTemplate).WithMany(p => p.MemoTemplateSections)
                .HasForeignKey(d => d.MemoTemplateId)
                .HasConstraintName("FK__MemoTempl__memoT__09A971A2");
        });

        modelBuilder.Entity<MemoTemplateUserRoleAssign>(entity =>
        {
            entity.HasKey(e => new { e.MemoTemplateId, e.RoleId });

            entity.ToTable("MemoTemplateUserRoleAssign");

            entity.Property(e => e.MemoTemplateAssignCreatedBy)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.MemoTemplateAssignCreatedDate).HasColumnType("datetime");
            entity.Property(e => e.MemoTemplateAssignModifiedBy)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.MemoTemplateAssignModifiedDate).HasColumnType("datetime");

            entity.HasOne(d => d.MemoTemplate).WithMany(p => p.MemoTemplateUserRoleAssigns)
                .HasForeignKey(d => d.MemoTemplateId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__MemoTempl__MemoT__08B54D69");
        });

        modelBuilder.Entity<MemoType>(entity =>
        {
            entity.Property(e => e.MemoTypeId).HasColumnName("memoTypeId");
            entity.Property(e => e.MemoTypeCode)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("memoTypeCode");
            entity.Property(e => e.MemoTypeCreatedBy)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("memoTypeCreatedBy");
            entity.Property(e => e.MemoTypeCreatedDate)
                .HasColumnType("datetime")
                .HasColumnName("memoTypeCreatedDate");
            entity.Property(e => e.MemoTypeIsActive)
                .HasDefaultValue(true)
                .HasColumnName("memoTypeIsActive");
            entity.Property(e => e.MemoTypeIsDel)
                .HasDefaultValue(false)
                .HasColumnName("memoTypeIsDel");
            entity.Property(e => e.MemoTypeModifiedBy)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("memoTypeModifiedBy");
            entity.Property(e => e.MemoTypeModifiedDate)
                .HasColumnType("datetime")
                .HasColumnName("memoTypeModifiedDate");
            entity.Property(e => e.MemoTypeName)
                .HasMaxLength(500)
                .IsUnicode(false)
                .HasColumnName("memoTypeName");
        });

        modelBuilder.Entity<Menu>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_tbMenus");

            entity.Property(e => e.Code)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDate).HasColumnType("datetime");
            entity.Property(e => e.Device)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.Icon).HasMaxLength(200);
            entity.Property(e => e.ModifiedDate).HasColumnType("datetime");
            entity.Property(e => e.Name)
                .IsRequired()
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.Url)
                .HasMaxLength(500)
                .IsUnicode(false)
                .HasColumnName("URL");
            entity.Property(e => e.Urltarget)
                .HasMaxLength(50)
                .HasColumnName("URLTarget");
        });

        modelBuilder.Entity<Module>(entity =>
        {
            entity.HasIndex(e => e.IsActive, "IX_Modules");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.ModuleTitle).HasMaxLength(300);
        });

        modelBuilder.Entity<OfficeLocation>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_OfficeLocation_Id");

            entity.ToTable("OfficeLocation");

            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.Location)
                .IsRequired()
                .HasMaxLength(200)
                .IsUnicode(false);
        });

        modelBuilder.Entity<ReportUsersMemoTemplate>(entity =>
        {
            entity.HasKey(e => new { e.UserId, e.MemoTemplateId }).HasName("PK__ReportUs__6AD81C91A3AABF64");

            entity.Property(e => e.UserId)
                .HasMaxLength(100)
                .IsUnicode(false);

            entity.HasOne(d => d.MemoTemplate).WithMany(p => p.ReportUsersMemoTemplates)
                .HasForeignKey(d => d.MemoTemplateId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__ReportUse__MemoT__4589517F");

            entity.HasOne(d => d.User).WithMany(p => p.ReportUsersMemoTemplates)
                .HasPrincipalKey(p => p.UserId)
                .HasForeignKey(d => d.UserId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__ReportUse__UserI__44952D46");
        });

        modelBuilder.Entity<Role>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_tblUserRoles");

            entity.Property(e => e.Code)
                .IsRequired()
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDate).HasColumnType("datetime");
            entity.Property(e => e.ModifiedDate).HasColumnType("datetime");
            entity.Property(e => e.Name)
                .IsRequired()
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.Notes)
                .IsRequired()
                .HasMaxLength(500)
                .IsUnicode(false);
        });

        modelBuilder.Entity<RoleMenu>(entity =>
        {
            entity.HasKey(e => new { e.RoleId, e.MenuId });

            entity.Property(e => e.CreatedDate).HasColumnType("datetime");
            entity.Property(e => e.ModifiedDate).HasColumnType("datetime");

            entity.HasOne(d => d.Menu).WithMany(p => p.RoleMenus)
                .HasForeignKey(d => d.MenuId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RoleMenus_Menus");

            entity.HasOne(d => d.Role).WithMany(p => p.RoleMenus)
                .HasForeignKey(d => d.RoleId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RoleMenus_Roles");
        });

        modelBuilder.Entity<ShiftLocation>(entity =>
        {
            entity.Property(e => e.EmployeeShiftCode)
                .HasMaxLength(255)
                .HasColumnName("Employee_Shift_Code");
            entity.Property(e => e.LocationCode)
                .HasMaxLength(255)
                .HasColumnName("Location_Code");
            entity.Property(e => e.LocationTxt)
                .HasMaxLength(255)
                .HasColumnName("Location_TXT");
            entity.Property(e => e.ShiftEnd).HasColumnName("Shift_End");
            entity.Property(e => e.ShiftStart).HasColumnName("Shift_Start");
        });

        modelBuilder.Entity<SubCategory>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_SubCategories_Id");

            entity.HasIndex(e => new { e.CategoryId, e.Title }, "UQ__SubCateg__DBC25C473C2F961E").IsUnique();

            entity.Property(e => e.Title)
                .IsRequired()
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.HasOne(d => d.Category).WithMany(p => p.SubCategories)
                .HasForeignKey(d => d.CategoryId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__SubCatego__Categ__04E4BC85");
        });

        modelBuilder.Entity<TemplateStatus>(entity =>
        {
            entity.HasKey(e => e.TemplateStatusId).HasName("PK_TemplateStatuses_TemplateStatusId");

            entity.HasIndex(e => e.TemplateStatusTitle, "UQ__Template__14DC57658F97E011").IsUnique();

            entity.Property(e => e.TemplateStatusTitle)
                .IsRequired()
                .HasMaxLength(300)
                .IsUnicode(false);
        });

        modelBuilder.Entity<User>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_tblUsers");

            entity.HasIndex(e => e.UserId, "KEY_Users_UserId").IsUnique();

            entity.HasIndex(e => e.UserId, "UQ__Users__1788CC4D0E8E84D3").IsUnique();

            entity.Property(e => e.Code)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDate).HasColumnType("datetime");
            entity.Property(e => e.Email)
                .IsRequired()
                .HasMaxLength(300)
                .IsUnicode(false);
            entity.Property(e => e.EmployeeCode)
                .HasMaxLength(15)
                .IsUnicode(false);
            entity.Property(e => e.MobileNumber)
                .IsRequired()
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.ModifiedDate).HasColumnType("datetime");
            entity.Property(e => e.Name)
                .IsRequired()
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.Note)
                .HasMaxLength(500)
                .IsUnicode(false);
            entity.Property(e => e.Password)
                .IsRequired()
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.Pin)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("PIN");
            entity.Property(e => e.UserId)
                .IsRequired()
                .HasMaxLength(100)
                .IsUnicode(false);
        });

        modelBuilder.Entity<UserRole>(entity =>
        {
            entity.HasKey(e => new { e.UserId, e.RoleId });

            entity.Property(e => e.CreatedDate).HasColumnType("datetime");
            entity.Property(e => e.ModifiedDate).HasColumnType("datetime");
            entity.Property(e => e.Notes)
                .HasMaxLength(500)
                .IsUnicode(false);

            entity.HasOne(d => d.Role).WithMany(p => p.UserRoles)
                .HasForeignKey(d => d.RoleId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_UserRoles_Roles");

            entity.HasOne(d => d.User).WithMany(p => p.UserRoles)
                .HasForeignKey(d => d.UserId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_UserRoles_Users");
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}